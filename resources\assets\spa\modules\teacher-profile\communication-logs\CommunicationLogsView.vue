<template lang="">
    <DialogPopup
        :visibleDialog="store.showDialog"
        :hideOnOverlayClick="true"
        :fixedActionBar="true"
        :width="'50%'"
        :maxWidth="'800px'"
        :secondaryBtnLabel="'Close'"
        :isDisabled="store.loading"
        @drawerclose="store.showDialog = false"
        :position="'right'"
        :pt="{ content: 'p-0' }"
    >
        <template #title>
            <div class="text-lg font-medium">Communication Log for {{ store.formData?.type }}</div>
        </template>
        <template #content>
            <div class="px-6 py-4">
                <div v-html="store.formData?.log"></div>
            </div>
        </template>
    </DialogPopup>
</template>
<script setup>
import DialogPopup from '@spa/components/KendoModals/SidebarDrawer.vue';
import { useStaffCommunicationLogStore } from '@spa/stores/modules/staff/useStaffCommunicationLogStore.js';

const store = useStaffCommunicationLogStore();
</script>
<style lang=""></style>
