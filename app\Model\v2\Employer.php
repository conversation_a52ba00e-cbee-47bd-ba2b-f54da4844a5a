<?php

namespace App\Model\v2;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Employer extends Model
{
    use HasFactory;

    protected $table = 'rto_employer';

    protected $fillable = [
        'college_id',
        'employer_name',
        'trading_name',
        'contact_person',
        'ABN',
        'industries_id',
        'address',
        'suburb',
        'country_id',
        'email',
        'fax',
        'postcode',
        'state',
        'phone',
        'mobile',
        'status',
        'created_by',
        'updated_by',
    ];

    public function getEmployerNameList($collegeId)
    {
        $arrEmployerList = Employer::where('college_id', '=', $collegeId)->get(['id as Id', 'employer_name as Name'])->toArray();

        // $result[''] = '- - No Employer - -';
        // foreach ($arrEmployerList as $row) {
        //     $result[$row->id] = $row->employer_name;
        // }
        return $arrEmployerList;
    }

    // Scope for filtering by college
    public function scopeCollegeId($query, $collegeId)
    {
        return $query->where('college_id', $collegeId);
    }

    // Relationship with industry
    public function industry()
    {
        return $this->belongsTo(\App\Model\v2\Industries::class, 'industries_id');
    }

    // Relationship with creator user
    public function creator()
    {
        return $this->belongsTo(\App\User::class, 'created_by');
    }

    // Relationship with updater user
    public function updater()
    {
        return $this->belongsTo(\App\User::class, 'updated_by');
    }

    // Filter scope for general search query
    public function scopeFilterQuery($query, $value)
    {
        if (!$value) {
            return $query;
        }

        $searchTerm = '%' . trim($value) . '%';

        return $query->where(function ($q) use ($searchTerm) {
            $q->where('employer_name', 'like', $searchTerm)
              ->orWhere('contact_person', 'like', $searchTerm)
              ->orWhere('email', 'like', $searchTerm)
              ->orWhere('phone', 'like', $searchTerm)
              ->orWhere('trading_name', 'like', $searchTerm)
              ->orWhere('state', 'like', $searchTerm)
              ->orWhere('ABN', 'like', $searchTerm);
        });
    }

    // Filter scope for status
    public function scopeFilterStatus($query, $value)
    {
        if ($value === null || $value === '') {
            return $query;
        }

        return $query->where('status', $value);
    }

    // Filter scope for industry
    public function scopeFilterIndustriesId($query, $value)
    {
        if ($value === null || $value === '') {
            return $query;
        }

        return $query->where('industries_id', $value);
    }
}
