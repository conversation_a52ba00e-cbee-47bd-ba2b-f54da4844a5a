<?php

namespace GalaxyAPI\Controllers;

use App\Model\v2\StaffCommunicationLog;
use GalaxyAPI\Requests\StaffCommunicationLogRequest;
use GalaxyAPI\Resources\StaffCommunicationLogResource;

class StaffCommunicationLogController extends CrudBaseController
{
    public function __construct()
    {
        $this->withAll = [
            'user',
            'staffTeacher',
            'setupType',
            'setupStatus',

        ];
        parent::__construct(
            model: StaffCommunicationLog::class,
            storeRequest: StaffCommunicationLogRequest::class,
            updateRequest: StaffCommunicationLogRequest::class,
            resource: StaffCommunicationLogResource::class,
        );
    }
}
