<div id="offerServiceAddedInfo" class="bg-gray-100" style="display: none; overflow: scroll;">
    <div id="studentServiceTabStrip" class="bg-gray-100">
        <ul class="flex bg-white md:!pl-8 !pl-0 !gap-8 bottomBorder">
            <li class="k-state-active"><span class="inline-block text-sm leading-5 px-1 py-3 text-gray-500">OSHC
                    General</span></li>
            <li><span class="inline-block text-sm leading-5 px-1 py-3 text-gray-500">Additional Service</span></li>
        </ul>
        <div class="oshc-general-li">
            <form id="oshcServices" name="oshcServices">
                <input type="hidden" name="student_id" value="{{ (isset($studentId)) ? $studentId : '' }}"
                    id="student_id" />
                <div class="flex bg-gray-100 px-6 pt-6 pb-20 space-x-12 overflow-y-auto h-full">
                    <div class="flex w-1/4">
                        <div
                            class="flex flex-col space-y-4 items-center justify-start py-4 bg-white shadow border rounded-md border-gray-200 w-1/4 fixed">
                            <ul class="flex flex-col items-start justify-start w-full bg-white oshc-bar">
                                <li class="inline-flex items-center justify-start w-full">
                                    <a href="#11"
                                        class="oshcServicesTab flex space-x-3 items-center justify-start flex-1 px-3 py-2 active">
                                        <p class="text-sm font-medium leading-5 text-gray-500">Overseas Student Health
                                            Cover</p>
                                    </a>
                                </li>
                                <li class="inline-flex items-center justify-start w-full">
                                    <a href="#22"
                                        class="oshcServicesTab flex space-x-3 items-center justify-start flex-1 px-3 py-2">
                                        <p class="text-sm font-medium leading-5 text-gray-500">How did you hear about
                                            us?</p>
                                    </a>
                                </li>
                                <li class="inline-flex items-center justify-start w-full">
                                    <a href="#33"
                                        class="oshcServicesTab flex space-x-3 items-center justify-start flex-1 px-3 py-2">
                                        <p class="text-sm font-medium leading-5 text-gray-500">Additional Information
                                            (Disability)</p>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="flex flex-row h-full bg-gray-100 w-3/4 mb-28">
                        <div class="space-y-6 w-full w-full offerServiceAddedInfo-wrap">
                            <div class="oshc-section">
                                <div id="11" class="w-full holder">

                                    <div id="oshcDetailsViewDiv"></div>
                                    <div id="oshcDetailsTemplate1"></div>
                                </div>

                            </div>
                            <div class="oshc-section">
                                <div id="22" class="w-full holder">
                                    <div id="howDidYouHearAboutUsDiv"></div>
                                    <div id="howDidYouHearAboutUs1"></div>
                                </div>
                            </div>
                            <div class="oshc-section">
                                <div id="33" class="w-full holder mb-28">
                                    <div id="additionalInformationDisabilityDiv"></div>
                                    <div id="additionalInformationDisability1"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="additional-li">
            <div class="flex flex-col w-full p-6 bg-gray-100 overflow-y-auto h-96">
                <div
                    class="w-full p-6 items-start justify-start shadow rounded-lg border bg-white space-y-2 emptyAdditionalService">
                    <div class="flex flex-col space-y-4 items-start justify-start w-full ">
                        <div class="inline-flex space-x-2 items-center justify-between w-full">
                            <div class="inline-flex space-x-2 items-center justify-start">
                                <p class="text-lg font-medium leading-normal text-gray-900">Additional Details</p>
                                <img src="{{ asset('v2/img/question.svg') }}" class="" alt="Help">
                            </div>
                        </div>
                    </div>
                    <div
                        class="inline-flex flex-col items-center justify-center py-6 bg-gray-50 rounded-lg w-full space-y-6">
                        <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M16.2488 0C17.4915 0 18.4988 1.00736 18.4988 2.25V17.75C18.4988 18.9926 17.4915 20 16.2488 20H10.6904C11.1168 19.5557 11.4806 19.051 11.7678 18.5H16.2488C16.663 18.5 16.9988 18.1642 16.9988 17.75V2.25C16.9988 1.83579 16.663 1.5 16.2488 1.5H5.75C5.33579 1.5 5 1.83579 5 2.25V9.07645C4.47679 9.15724 3.97417 9.30042 3.5 9.49816V2.25C3.5 1.00736 4.50736 0 5.75 0H16.2488Z"
                                fill="#1890FF" />
                            <path
                                d="M19.5 13.0019H20.25C20.6297 13.0019 20.9435 13.2841 20.9932 13.6502L21 13.7519V15.25C21 15.6297 20.7178 15.9435 20.3518 15.9932L20.25 16H19.5V13.0019Z"
                                fill="#1890FF" />
                            <path
                                d="M19.5 9.00194H20.25C20.6297 9.00194 20.9435 9.28409 20.9932 9.65017L21 9.75194V11.25C21 11.6297 20.7178 11.9435 20.3518 11.9932L20.25 12H19.5V9.00194Z"
                                fill="#1890FF" />
                            <path
                                d="M19.5 5.00194H20.25C20.6297 5.00194 20.9435 5.28409 20.9932 5.65017L21 5.75194V7.25C21 7.6297 20.7178 7.94349 20.3518 7.99315L20.25 8H19.5V5.00194Z"
                                fill="#1890FF" />
                            <path
                                d="M14.7488 3C15.163 3 15.4988 3.33579 15.4988 3.75V6.2485C15.4988 6.66272 15.163 6.9985 14.7488 6.9985H7.25C6.83579 6.9985 6.5 6.66272 6.5 6.2485V3.75C6.5 3.33579 6.83579 3 7.25 3H14.7488ZM13.9988 4.5H8V5.4985H13.9988V4.5Z"
                                fill="#1890FF" />
                            <path
                                d="M11.5 15.5C11.5 12.4624 9.03757 10 6 10C2.96243 10 0.5 12.4624 0.5 15.5C0.5 18.5376 2.96243 21 6 21C9.03757 21 11.5 18.5376 11.5 15.5ZM6.50065 16L6.50111 18.5035C6.50111 18.7797 6.27725 19.0035 6.00111 19.0035C5.72497 19.0035 5.50111 18.7797 5.50111 18.5035L5.50065 16H2.9956C2.71973 16 2.49609 15.7762 2.49609 15.5C2.49609 15.2239 2.71973 15 2.9956 15H5.50046L5.5 12.4993C5.5 12.2231 5.72386 11.9993 6 11.9993C6.27614 11.9993 6.5 12.2231 6.5 12.4993L6.50046 15H8.99659C9.27246 15 9.49609 15.2239 9.49609 15.5C9.49609 15.7762 9.27246 16 8.99659 16H6.50065Z"
                                fill="#1890FF" />
                        </svg>
                        <p class="text-sm text-gray-700 leading-5 font-normal">You have not request additional service.
                        </p>
                        <div class="inline-flex space-x-2 items-start justify-center">
                            <button id="new_addional_service" class="btn-primary">
                                <img src="{{ asset('v2/img/plus-white.svg') }}" class="" alt="">
                                <p class="text-sm font-medium leading-4 text-white uppercase">Request Additional Service
                                </p>
                            </button>
                        </div>
                    </div>
                </div>
                <div
                    class="w-full p-6 items-start justify-start shadow rounded-lg border bg-white additionalServiceList">
                    <div class="flex flex-col space-y-4 items-start justify-start w-full pb-5">
                        <div class="inline-flex space-x-2 items-center justify-between w-full">
                            <div class="inline-flex space-x-2 items-center ">
                                <p class="text-lg font-medium leading-normal text-gray-900">Additional Details</p>
                            </div>
                            <div class="inline-flex space-x-2 items-start">
                                <button id="new_addional_service" class="btn-primary">
                                    <img src="{{ asset('v2/img/plus-white.svg') }}" class="" alt="">
                                    <p class="text-sm font-medium leading-4 text-white uppercase">Request Additional
                                        Service</p>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div id="studentAdditionalServiceGrid"
                        class="tw-table tw-table__borderless tw-table__header--borderless tw-table--scrollbar-hide">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="additionalServiceReqModal" class="wizardModal" style="display: none">
    <form id="additionalServiceReqForm"></form>
</div>

<div id="editAdditionalServiceReqModal" class="wizardModal" style="display: none">
    <form id="editAdditionalServiceReqForm"></form>
</div>

<div id="deleteAdditionalServiceModal"></div>

<!-- TEMPLATES:RENDER OSHC -->
@include('v2.sadmin.student.templates.header.oshc.oshc-details')
@include('v2.sadmin.student.templates.header.oshc.oshc-detail-view')
@include('v2.sadmin.student.templates.header.oshc.hear-about')
@include('v2.sadmin.student.templates.header.oshc.hear-about-view')
@include('v2.sadmin.student.templates.header.oshc.additional-info')
@include('v2.sadmin.student.templates.header.oshc.additional-info-view')
@include('v2.sadmin.student.templates.header.oshc.additional-action')