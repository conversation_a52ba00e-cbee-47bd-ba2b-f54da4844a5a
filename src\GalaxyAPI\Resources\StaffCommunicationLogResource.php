<?php

namespace GalaxyAPI\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class StaffCommunicationLogResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'type_id' => $this->type,
            'type' => $this->whenLoaded('setupType', function () {
                return $this->setupType->value;
            }),
            'status_id' => $this->status,
            'status' => $this->whenLoaded('setupStatus', function () {
                return $this->setupStatus->value;
            }),
            'comment_by' => $this->whenLoaded('user', function () {
                return [
                    'id' => $this->user->id,
                    'full_name' => $this->user?->name,
                    'username' => $this->user->username,
                ];
            }),
            'staff_id' => $this->staff_id,
            'staff' => $this->whenLoaded('staff', function () {
                return [
                    'id' => $this->staff->id,
                    'full_name' => $this->staff->name_title.' '.$this->staff->first_name.' '.$this->staff->last_name,
                ];
            }),

            'log' => $this->log,
            'created_at' => $this->created_at,
        ];
    }
}
