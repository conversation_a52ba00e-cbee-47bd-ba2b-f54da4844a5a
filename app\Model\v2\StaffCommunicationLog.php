<?php

namespace App\Model\v2;

use App\Model\Traits\StaffCommunicationLogFilterTrait;
use Illuminate\Database\Eloquent\Model;

class StaffCommunicationLog extends Model
{
    use StaffCommunicationLogFilterTrait;

    protected $table = 'rto_staff_communication';

    protected $fillable = ['id ', 'college_id', 'today_date', 'staff_id', 'comment_by', 'type', 'status', 'log', 'visiblity', 'created_at', 'updated_at', 'created_by', 'updated_by'];

    const SECTION_ID = 5;                     // 5 for Diary-Staff as per DB:rto_setup_section_option

    const TYPE_ID = 6;                        // 6 for type_name="Type" as per DB:rto_setup_section_type

    const STATUS_ID = 7;                      // 7 for type_name="Status" as per DB:rto_setup_section_type

    const TYPE_NAME = 'General';             // as per DB:rto_default_setup_section

    const STATUS_NAME = 'Informed';          // as per DB:rto_default_setup_section

    public function addStaffCommunicationLog($request, $staff_id, $college_id, $login_id)
    {

        $comment_by = $userId = $login_id;
        $log = $request->input('log');
        // $userId = Auth::user()->id;
        $arrTopicList = Config::get('constants.arrTopicList');
        $topic = $arrTopicList[$request->input('topic')];

        $email_cc = [];
        if (! empty($request->input('email_cc'))) {
            $email_cc[] = $request->input('email_cc');
        }
        if (! empty($request->input('email'))) {
            $email_cc = array_merge($email_cc, $request->input('email'));
        }

        $email_cc_comma = implode(',', $email_cc);

        if (! empty($request->input('log'))) {
            $log = 'Send to : '.$request->input('email_to')
                .'<br> CC : '.$email_cc_comma
                .'<br> From email : '.$request->input('email_from')
                .'<br> Subject : '.$topic
                .'<br>  '.$request->input('log');
        }

        // Start Default Status & Type/Category ID
        $sectionId = self::SECTION_ID;                     // 5 for Diary-Staff as per DB:rto_setup_section_option
        $typeId = self::TYPE_ID;                        // 6 for type_name="Type" as per DB:rto_setup_section_type
        $statusId = self::STATUS_ID;                      // 7 for type_name="Status" as per DB:rto_setup_section_type
        $type_name = self::TYPE_NAME;             // as per DB:rto_default_setup_section
        $status_name = self::STATUS_NAME;          // as per DB:rto_default_setup_section
        $objSetupSection = new SetupSection;
        $defaultType = $objSetupSection->getSetupSectionId($college_id, $sectionId, $typeId, $type_name);           // replace by 11
        $defaultStatus = $objSetupSection->getSetupSectionId($college_id, $sectionId, $statusId, $status_name);     // replace by 12
        // End Default Status & Type/Category ID

        StaffCommunicationLog::create([
            'college_id' => $college_id,
            'today_date' => now()->format('l, d F Y'),
            'staff_id' => $staff_id,
            'comment_by' => $comment_by,
            'type' => $request->input('type', $defaultType),
            'status' => $request->input('status', $defaultStatus),
            'log' => $log,
            'created_by' => $userId,
            'updated_by' => $userId,
        ]);

        return true;
    }
}
