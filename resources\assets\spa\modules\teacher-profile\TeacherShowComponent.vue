<template>
    <div class="flex flex-col">
        <ProfileHeaderWithTabs :tabs="tabs" :currentTab="activeTab">
            <template #titleSlot>
                <h2 class="text-2xl font-medium">{{ store.formData?.full_name }}</h2>
            </template>
            <template #buttongroup>
                <Button :variant="'primary'" size="xs">
                    <span :class="'text-white'">
                        <icon :name="'mail'" :fill="'#ffffff'" size="16" />
                    </span>
                    <span>Send Mail</span>
                </Button>
                <Button :variant="'primary'" size="xs">
                    <span :class="'text-white'">
                        <icon :name="'sms'" />
                    </span>
                    <span>Send SMS</span>
                </Button>
            </template>
        </ProfileHeaderWithTabs>
        <div class="flex-1 overflow-y-auto px-6 py-5">
            <ProfileDetailCardComponent v-if="activeTab === 'profile'" />
            <CommunicationLogsListComponent v-if="activeTab === 'communication-logs'" />
            <TimetableListComponent v-if="activeTab === 'timetable'" />
            <!-- <DocumentsListComponent v-if="activeTab === 'documents'" />
            <LeaveInfoComponent v-if="activeTab === 'leave-info'" /> -->
        </div>
    </div>
</template>
<script setup>
import ProfileHeaderWithTabs from '@spa/modules/common/ProfileHeaderWithTabs.vue';
import Button from '@spa/components/Buttons/Button.vue';
import { ref, computed, onMounted } from 'vue';
import { usePage } from '@inertiajs/vue3';
import CommunicationLogsListComponent from './communication-logs/CommunicationLogsListComponent.vue';
import ProfileDetailCardComponent from './profile/ProfileDetailCardComponent.vue';
import { useTeacherStore } from '@spa/stores/modules/teacher/useTeacherStore.js';
import TimetableListComponent from './timetable/TimetableListComponent.vue';

let props = defineProps();

const store = useTeacherStore();

const $page = usePage();

const activeTab = computed(() => {
    const path = $page.url;
    const slug = path.split('/').pop();
    if (!isNaN(Number(slug))) {
        return 'profile';
    }
    return slug || 'profile';
});

const teacherId = computed(() => $page.props.params.id);

const tabs = [
    {
        name: 'Profile',
        slug: 'profile',
        route: route('spa.teacher-profile', { id: teacherId.value }),
    },
    {
        name: 'Communication Logs',
        slug: 'communication-logs',
        route: route('spa.teacher-communication-logs', { id: teacherId.value }),
    },
    {
        name: 'Timetable',
        slug: 'timetable',
        route: route('spa.teacher-timetable', { id: teacherId.value }),
    },
    {
        name: 'Teacher Matrix',
        slug: 'teacher-matrix',
        route: route('spa.teacher-matrix', { id: teacherId.value }),
    },
    {
        name: 'Documents',
        slug: 'documents',
        route: route('spa.teacher-documents', { id: teacherId.value }),
    },
    {
        name: 'Leave Info',
        slug: 'leave-info',
        route: route('spa.teacher-leave-info', { id: teacherId.value }),
    },
];

onMounted(() => {
    store.fetchDataById(teacherId.value);
});
</script>
<style lang=""></style>
