<template lang="">
    <AsyncForm
        :form-config="formFields"
        :initial-values="store.formData"
        @submit="onSubmit"
        @submitcheck="handleSubmitclick"
        @change="onChange"
        :type="'kendo'"
        :layout="{ cols: 2, gap: 16 }"
        :orientation="'vertical'"
        :dialogTitle="'Add Register Improvement'"
        :override="true"
        :store="store"
        max-width="600px"
    >
        <TeacherFormContent :store="store" />
    </AsyncForm>
</template>
<script setup>
import { useTeacherStore } from '@spa/stores/modules/teacher/useTeacherStore.js';
import AsyncForm from '@spa/components/AsyncComponents/Form/AsyncFormPopup.vue';
import TeacherFormContent from '@spa/modules/teacher/partials/TeacherFormContent.vue';

const store = useTeacherStore();

const handleSubmitclick = (value) => {
    store.isFormValid = value;
};
</script>
<style lang=""></style>
