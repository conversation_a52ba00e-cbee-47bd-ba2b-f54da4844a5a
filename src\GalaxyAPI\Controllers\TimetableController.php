<?php

namespace GalaxyAPI\Controllers;

use App\Model\v2\Timetable;
use GalaxyAPI\Requests\TimetableRequest;
use GalaxyAPI\Resources\TimetableResource;

class TimetableController extends CrudBaseController
{
    public function __construct()
    {
        $this->withAll = [
            'semester',
            // 'courseType',
            // 'campus',
            // 'venue',
            // 'classroom',
            // 'subject',
            // 'batch',
            // 'teacher',
            // 'creator',
            // 'updater',
        ];
        // $this->loadAll = [
        //     'semester',
        //     'courseType',
        //     'campus',
        //     'venue',
        //     'classroom',
        //     'subject',
        //     'batch',
        //     'teacher',
        //     'creator',
        //     'updater',
        // ];

        parent::__construct(
            model: Timetable::class,
            storeRequest: TimetableRequest::class,
            updateRequest: TimetableRequest::class,
            resource: TimetableResource::class,
        );
    }
}
