<script setup>
import TeacherShowComponent from '@spa/modules/teacher-profile/TeacherShowComponent.vue';
import Layout from '@spa/pages/Layouts/Layout';
import { Head } from '@inertiajs/vue3';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
</script>
<template>
    <Layout :no-spacing="true" :loading="false">
        <Head title="Teachers List" />
        <template v-slot:pageTitleContent>
            <PageTitleContent title="Teachers List" :back="false" />
        </template>
        <div class="h-screen-header">
            <TeacherShowComponent />
        </div>
    </Layout>
</template>

<style scoped></style>
