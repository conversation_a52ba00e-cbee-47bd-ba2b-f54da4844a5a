<?php

namespace GalaxyAPI\Controllers;

use App\Model\v2\SetupSection;
use GalaxyAPI\Requests\SetupSectionRequest;
use GalaxyAPI\Resources\SetupSectionResource;

class SetupSectionController extends CrudBaseController
{
    public function __construct()
    {
        parent::__construct(
            model: SetupSection::class,
            storeRequest: SetupSectionRequest::class,
            updateRequest: SetupSectionRequest::class,
            resource: SetupSectionResource::class,
        );
    }
}
