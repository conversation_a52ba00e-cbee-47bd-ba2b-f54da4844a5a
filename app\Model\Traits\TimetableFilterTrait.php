<?php

namespace App\Model\Traits;

use App\Model\v2\CampusVenue;
use App\Model\v2\Classroom;
use App\Model\v2\CollegeCampus;
use App\Model\v2\CourseBatch;
use App\Model\v2\CourseType;
use App\Model\v2\Semester;
use App\Model\v2\Staff;
use App\Model\v2\Subject;
use App\Model\v2\Users;

trait TimetableFilterTrait
{
    public function scopeFilterQuery($query, $value)
    {
        if (! $value) {
            return $query;
        }

        $searchTerm = '%'.trim($value).'%';

        return $query->where(function ($q) use ($searchTerm) {
            $q->where('name', 'like', $searchTerm);
        });
    }

    public function creator()
    {
        return $this->belongsTo(Users::class, 'created_by');
    }

    public function updater()
    {
        return $this->belongsTo(Users::class, 'updated_by');
    }

    public function semester()
    {
        return $this->belongsTo(Semester::class, 'semester_id');
    }

    public function courseType()
    {
        return $this->belongsTo(CourseType::class, 'course_type_id');
    }

    public function campus()
    {
        return $this->belongsTo(CollegeCampus::class, 'campus_id');
    }

    public function venue()
    {
        return $this->belongsTo(CampusVenue::class, 'venue_id');
    }

    public function classroom()
    {
        return $this->belongsTo(Classroom::class, 'classroom_id');
    }

    public function subject()
    {
        return $this->belongsTo(Subject::class, 'subject_id');
    }

    public function batch()
    {
        return $this->belongsTo(CourseBatch::class, 'batch');
    }

    public function teacher()
    {
        return $this->belongsTo(Staff::class, 'teacher_id');
    }
}
