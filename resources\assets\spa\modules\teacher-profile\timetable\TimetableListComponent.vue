<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :has-create-action="false"
        :has-export="true"
        :has-filters="false"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
            }
        "
        :has-actions="false"
    >
    </AsyncGrid>
    <TimetableForm />
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { ref, onMounted } from 'vue';
import { useTimetableStore } from '@spa/stores/modules/timetable/useTimetableStore.js';
import TimetableForm from '@spa/modules/teacher-profile/timetable/TimetableForm.vue';

const store = useTimetableStore();

const columns = [
    {
        field: 'subject',
        title: 'Subject',
        width: '200px',
    },
    {
        field: 'term',
        title: 'Term',
    },
    {
        field: 'batch',
        title: 'Batch',
    },
    {
        field: 'mode',
        title: 'Mode',
    },
    {
        field: 'room',
        title: 'Room',
    },
    {
        field: 'day',
        title: 'Day',
    },
    {
        field: 'class_duration',
        title: 'Class Duration',
    },
    {
        field: 'time_range',
        title: 'Time',
    },
    {
        field: 'attendance_type',
        title: 'Attendance Type',
    },
    // Add more columns as needed
];

const initFilters = () => {
    store.filters = {};
};

onMounted(() => {
    initFilters();
});
</script>
