import useCommonStore from '@spa/stores/modules/commonStore.js';
import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useStaffCommunicationLogStore = defineStore('useStaffCommunicationLogStore', () => {
    const storeUrl = ref('v2/tenant/staff-communication-log');

    const commonStoreProps = useCommonStore(storeUrl.value);

    return {
        ...commonStoreProps,
    };
});
