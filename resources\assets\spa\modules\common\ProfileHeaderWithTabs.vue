<template lang="">
    <div :class="rootClass">
        <template v-if="$slots.titleSlot" :class="titleClass">
            <slot name="titleSlot" />
        </template>
        <h2 v-else :class="titleClass">{{ getTitle }}</h2>
        <div v-if="$slots.buttongroup" :class="buttonGroupClass">
            <slot name="buttongroup" />
        </div>
        <div :class="buttonGroupClass" v-else-if="actions.length > 0">
            <template v-for="(button, key) in actionButtons" :key="key">
                <Button
                    :variant="button.variant || 'secondary'"
                    size="xs"
                    :class="
                        button.label == 'Delete Files' ? 'border-red-400 [&_svg]:text-red-400' : ''
                    "
                    @click="handleHeaderAction(button.slug)"
                >
                    <span :class="button.variant === 'primary' ? 'text-white' : 'text-gray-400'">
                        <icon :name="button.icon" v-if="button.icon" />
                    </span>
                    <span>{{ button.label }}</span></Button
                >
            </template>
        </div>
    </div>
    <HeaderTabs :tabs="tabs" :current="currentTab" />
</template>
<script>
import { twMerge } from 'tailwind-merge';
import Button from '@spa/components/Buttons/Button.vue';
import HeaderTabs from '@spa/modules/common/HeaderTabs.vue';

export default {
    props: {
        title: {
            type: String,
            default: 'Manage Roles',
        },
        tabs: {
            type: Object,
            default: () => ({}),
        },
        actions: {
            type: Object,
            default: () => ({}),
        },
        pt: {
            type: Object,
            default: () => ({}),
        },
        hasActions: {
            type: Boolean,
            default: false,
        },
        currentTab: {
            type: String,
            default: '',
        },
    },
    components: {
        Button,
        HeaderTabs,
    },
    computed: {
        rootClass() {
            return twMerge(
                'flex items-center justify-between px-6 py-4 md:px-8 bg-white',
                this.pt.root
            );
        },
        titleClass() {
            return twMerge('text-2xl font-medium', this.pt.title);
        },
        buttonGroupClass() {
            return twMerge('flex justify-end items-center gap-2', this.pt.buttongroup);
        },
        actionButtons() {
            return this.actions;
        },
        getTitle() {
            return `${this.title}`;
        },
    },
    methods: {
        handleHeaderAction(key) {
            this.$emit('update:actions', key);
        },
    },
};
</script>
<style lang=""></style>
