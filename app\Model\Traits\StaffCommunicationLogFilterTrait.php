<?php

namespace App\Model\Traits;

use App\Model\v2\SetupSection;
use App\Model\v2\Staff;
use App\Model\v2\Users;

trait StaffCommunicationLogFilterTrait
{
    public function scopeFilterQuery($query, $value)
    {
        if (empty($value)) {
            return $query;
        }

        $searchTerm = '%'.trim($value).'%';

        return $query->where(function ($q) use ($searchTerm) {
            $q->where('course_name', 'like', $searchTerm)
                ->orWhere('course_duration', 'like', $searchTerm)
                ->orWhere('delivery_target', 'like', $searchTerm)
                ->orWhere('course_code', 'like', $searchTerm)
                ->orWhere('national_code', 'like', $searchTerm);
        });
    }

    public function scopeCollegeId(
        $query,
        $value
    ) {
        if (empty($value)) {
            return $query;
        }

        return $query->where('college_id', $value);
    }

    public function setupType()
    {
        return $this->belongsTo(SetupSection::class, 'type', 'id');
    }

    public function setupStatus()
    {
        return $this->belongsTo(SetupSection::class, 'status', 'id');
    }

    public function user()
    {
        return $this->belongsTo(Users::class, 'comment_by', 'id');
    }

    public function staffTeacher()
    {
        return $this->belongsTo(Staff::class, 'staff_id', 'id');
    }
}
