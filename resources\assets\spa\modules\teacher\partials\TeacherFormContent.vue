<template>
    <div class="flex-1 space-y-6 overflow-y-auto px-6 py-4">
        <Card :pt="{ root: 'relative p-4 rounded-lg' }">
            <template #header>
                <h2 class="mb-4 text-lg font-bold text-gray-700">Personal Information</h2>
            </template>
            <template #content>
                <fieldset>
                    <div class="grid flex-1 grid-cols-2 gap-6 overflow-y-auto">
                        <field
                            :id="'name_title'"
                            :name="'name_title'"
                            :label="'Name Title'"
                            :component="FormDropDown"
                            :data-items="['Mr.', 'Ms.', 'Mrs.', 'Miss.', 'Dr.']"
                            v-model="store.formData['name_title']"
                        />
                        <div class=""></div>
                        <field
                            :id="'first_name'"
                            :name="'first_name'"
                            :label="'First Name'"
                            :component="FormInput"
                            v-model="store.formData['first_name']"
                            :required="true"
                            :validator="requiredtrue"
                        />
                        <field
                            :id="'last_name'"
                            :name="'last_name'"
                            :label="'Last Name'"
                            :component="FormInput"
                            v-model="store.formData['last_name']"
                            :required="true"
                            :validator="requiredtrue"
                        />
                        <div class="col-span-2 h-px bg-gray-300"></div>
                        <field
                            :id="'address'"
                            :name="'address'"
                            :label="'Address'"
                            :component="FormInput"
                            v-model="store.formData['address']"
                            :pt="{ root: 'col-span-2' }"
                            :required="true"
                            :validator="requiredtrue"
                        />
                        <field
                            :id="'country'"
                            :name="'country'"
                            :label="'Country'"
                            :component="FormDropDown"
                            :data-items="countryStore.all"
                            :text-field="'name'"
                            :data-item-key="'id'"
                            :value-field="'id'"
                            :value-primitive="true"
                            :default-item="{
                                name: 'Select Country',
                                id: null,
                            }"
                            v-model="store.formData['country']"
                            :required="true"
                            :validator="requiredtrue"
                        />

                        <field
                            :id="'city_town'"
                            :name="'city_town'"
                            :label="'City/Town/Suburb'"
                            :component="FormInput"
                            v-model="store.formData['city_town']"
                            :required="true"
                            :validator="requiredtrue"
                        />
                        <field
                            :id="'state'"
                            :name="'state'"
                            :label="'State'"
                            :component="FormInput"
                            v-model="store.formData['state']"
                            :required="true"
                            :validator="requiredtrue"
                        />
                        <field
                            :id="'postcode'"
                            :name="'postcode'"
                            :label="'Post Code'"
                            :component="FormInput"
                            v-model="store.formData['postcode']"
                            :required="true"
                            :validator="requiredtrue"
                        />
                        <div class="col-span-2 h-px bg-gray-300"></div>
                        <field
                            :id="'phone'"
                            :name="'phone'"
                            :label="'Phone Number'"
                            :component="FormInput"
                            v-model="store.formData['phone']"
                            :required="true"
                            :validator="mobiletrue"
                        />
                        <field
                            :id="'mobile'"
                            :name="'mobile'"
                            :label="'Mobile Number'"
                            :component="FormInput"
                            v-model="store.formData['mobile']"
                            :required="true"
                            :validator="mobiletrue"
                        />
                        <field
                            :id="'email'"
                            :name="'email'"
                            :type="'email'"
                            :label="'Email'"
                            :component="FormInput"
                            v-model="store.formData['email']"
                            :required="true"
                            :validator="emailtrue"
                        />
                        <field
                            :id="'personal_email'"
                            :name="'personal_email'"
                            :type="'email'"
                            :label="'Personal Email'"
                            :component="FormInput"
                            v-model="store.formData['personal_email']"
                        />

                        <field
                            :id="'signatory_text'"
                            :name="'signatory_text'"
                            :label="'Signatory Text'"
                            :component="FormInput"
                            v-model="store.formData['signatory_text']"
                            :pt="{ root: 'col-span-2' }"
                        />
                    </div>
                </fieldset>
            </template>
        </Card>
        <Card :pt="{ root: 'relative p-4 rounded-lg' }">
            <template #header>
                <h2 class="mb-4 text-lg font-bold text-gray-700">Employment Information</h2>
            </template>
            <template #content>
                <fieldset>
                    <div class="grid flex-1 grid-cols-3 gap-6 overflow-y-auto">
                        <field
                            :id="'staff_number'"
                            :name="'staff_number'"
                            :label="'Staff Number(For Pay Roll)'"
                            :component="FormInput"
                            v-model="store.formData['staff_number']"
                            :pt="{ root: 'col-span-2' }"
                            :required="true"
                            :validator="requiredtrue"
                        />
                        <div class=""></div>
                        <field
                            :id="'position'"
                            :name="'position'"
                            :label="'Position'"
                            :component="FormDropDown"
                            :data-items="['Teacher']"
                            :disabled="true"
                            :default-item="'Teacher'"
                            v-model="store.formData['position']"
                            :pt="{ root: 'col-span-2' }"
                        />
                        <field
                            :id="'is_active'"
                            :name="'is_active'"
                            :label="'Is Teacher Active'"
                            :component="FormSwitch"
                            :value-label="true"
                            :default-checked="true"
                            :default-value="true"
                            v-model="store.formData['is_active']"
                            :pt="{ root: 'col-span-2' }"
                        />
                    </div>
                </fieldset>
            </template>
        </Card>
        <Card :pt="{ root: 'relative p-4 rounded-lg space-y-4' }">
            <template #header>
                <h2 class="text-lg font-bold text-gray-700">TCSI Data Report</h2>
                <span class="text-xs italic text-red-500"
                    >* Fields are required for higherEd only.</span
                >
            </template>
            <template #content>
                <fieldset>
                    <div class="grid flex-1 grid-cols-2 gap-6 overflow-y-auto">
                        <field
                            :id="'birth_date'"
                            :name="'birth_date'"
                            :label="'DOB'"
                            :component="FormDatePicker"
                            v-model="store.formData['birth_date']"
                            :hint="'[ E314 Date Of Birth ]'"
                            :format="'dd-MM-yyyy'"
                            :emit-format="'yyyy-MM-dd'"
                            :validator="validDate"
                            :required="true"
                        />
                        <field
                            :id="'joining_date'"
                            :name="'joining_date'"
                            :label="'Joining Date'"
                            :component="FormDatePicker"
                            v-model="store.formData['joining_date']"
                            :hint="'[ E505 Appointment Term ]'"
                            :format="'dd-MM-yyyy'"
                            :emit-format="'yyyy-MM-dd'"
                        />
                        <field
                            :id="'gender'"
                            :name="'gender'"
                            :label="'Gender'"
                            :component="FormDropDown"
                            :data-items="genderOptions"
                            :default-item="{
                                text: 'Select Gender',
                                value: null,
                            }"
                            :text-field="'text'"
                            :data-item-key="'value'"
                            :value-field="'value'"
                            :value-primitive="true"
                            v-model="store.formData['gender']"
                            :hint="'[ E315 Gender Code ]'"
                        />
                        <field
                            :id="'atsi_code'"
                            :name="'atsi_code'"
                            :label="'ATSI Code'"
                            :component="FormDropDown"
                            :data-items="atsiCodesOptions"
                            :default-item="{
                                text: 'Select ATSI Code',
                                value: null,
                            }"
                            :text-field="'text'"
                            :data-item-key="'value'"
                            :value-field="'value'"
                            :value-primitive="true"
                            v-model="store.formData['atsi_code']"
                            :hint="'[ E316 ATSI Code ]'"
                        />
                        <field
                            :id="'highest_qualification_code'"
                            :name="'highest_qualification_code'"
                            :label="'Highest Qualification Code'"
                            :component="FormDropDown"
                            :data-items="highestQualificationOptions"
                            :default-item="{
                                text: 'Select Highest Qualification Code',
                                value: null,
                            }"
                            :text-field="'text'"
                            :data-item-key="'value'"
                            :value-field="'value'"
                            :value-primitive="true"
                            v-model="store.formData['highest_qualification_code']"
                            :hint="'[ E501 Highest Qualification Code ]'"
                        />
                        <field
                            :id="'highest_qualification_place_code'"
                            :name="'highest_qualification_place_code'"
                            :label="'Highest Qualification Place Code'"
                            :component="FormDropDown"
                            :data-items="highestQualificationPlaceOptions"
                            :default-item="{
                                text: 'Select Highest Qualification Place Code',
                                value: null,
                            }"
                            :text-field="'text'"
                            :data-item-key="'value'"
                            :value-field="'value'"
                            :value-primitive="true"
                            v-model="store.formData['highest_qualification_place_code']"
                            :hint="'[ E502 Highest Qualification Place Code ]'"
                        />
                        <field
                            :id="'work_contract_code'"
                            :name="'work_contract_code'"
                            :label="'Work Contract Code'"
                            :component="FormDropDown"
                            :data-items="workContractOptions"
                            :default-item="{
                                text: 'Select Work Contract Code',
                                value: null,
                            }"
                            :text-field="'text'"
                            :data-item-key="'value'"
                            :value-field="'value'"
                            :value-primitive="true"
                            v-model="store.formData['work_contract_code']"
                            :hint="'[ E506 Work Contract Code ]'"
                        />
                        <field
                            :id="'staff_work_level_code'"
                            :name="'staff_work_level_code'"
                            :label="'Staff Work Level Code'"
                            :component="FormDropDown"
                            :data-items="workLevelOptions"
                            :default-item="{
                                text: 'Select Staff Work Level Code',
                                value: null,
                            }"
                            :text-field="'text'"
                            :data-item-key="'value'"
                            :value-field="'value'"
                            :value-primitive="true"
                            v-model="store.formData['staff_work_level_code']"
                            :hint="'[ E408 Staff Work Level Code ]'"
                        />
                        <field
                            :id="'organisational_unit_code'"
                            :name="'organisational_unit_code'"
                            :label="'Organisational Unit Code'"
                            :component="FormDropDown"
                            :data-items="unitCodeOptions"
                            :default-item="{
                                text: 'Select Organisational Unit Code',
                                value: null,
                            }"
                            :text-field="'text'"
                            :data-item-key="'value'"
                            :value-field="'value'"
                            :value-primitive="true"
                            v-model="store.formData['organisational_unit_code']"
                            :hint="'[ E510 Organisational Unit Code ]'"
                        />
                        <field
                            :id="'work_sector_code'"
                            :name="'work_sector_code'"
                            :label="'Work Sector Code'"
                            :component="FormDropDown"
                            :data-items="workSectorOptions"
                            :default-item="{
                                text: 'Select Work Sector Code',
                                value: null,
                            }"
                            :text-field="'text'"
                            :data-item-key="'value'"
                            :value-field="'value'"
                            :value-primitive="true"
                            v-model="store.formData['work_sector_code']"
                            :hint="'[ E511 Work Sector Code ]'"
                        />
                        <field
                            :id="'function_code'"
                            :name="'function_code'"
                            :label="'Function Code'"
                            :component="FormDropDown"
                            :data-items="functionOptions"
                            :default-item="{
                                text: 'Select Function Code',
                                value: null,
                            }"
                            :text-field="'text'"
                            :data-item-key="'value'"
                            :value-field="'value'"
                            :value-primitive="true"
                            v-model="store.formData['function_code']"
                            :hint="'[ E412 Function Code ]'"
                        />
                    </div>
                </fieldset>
            </template>
        </Card>
        <Card :pt="{ root: 'relative p-4 rounded-lg' }" v-if="!isEdit">
            <template #header>
                <h2 class="mb-4 text-lg font-bold text-gray-700">User Account Information</h2>
            </template>
            <template #content>
                <fieldset>
                    <div class="grid flex-1 grid-cols-3 gap-6 overflow-y-auto">
                        <div class="col-span-2 space-y-6">
                            <field
                                :id="'username'"
                                :name="'username'"
                                :label="'Username'"
                                :component="FormInput"
                                v-model="store.formData['username']"
                                :validator="requiredtrue"
                                :required="true"
                            />
                            <field
                                :id="'password'"
                                :name="'password'"
                                :label="'Password'"
                                :component="FormInput"
                                v-model="store.formData['password']"
                                :validator="requiredtrue"
                                :required="true"
                            />
                        </div>
                    </div>
                </fieldset>
            </template>
        </Card>
    </div>
</template>
<script setup>
import { inject, ref, computed, watch } from 'vue';
import { Field } from '@progress/kendo-vue-form';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import FormDatePicker from '@spa/components/KendoInputs/FormDatePicker.vue';
import FormSwitch from '@spa/components/KendoInputs/FormSwitch.vue';
import {
    requiredtrue,
    emailtrue,
    mobiletrue,
} from '@spa/services/validators/kendoCommonValidator.js';
import Card from '@spa/components/Card/Card.vue';
import { tcsiConfig } from '@spa/config/tcsiConfig.js';
import { useCountryStore } from '@spa/stores/modules/config/useCountryStore.js';
const kendoForm = inject('kendoForm', {});

const props = defineProps({
    store: {
        type: Object,
        required: true,
    },
});

const isEdit = computed(() => {
    return !!props.store.formData.id;
});

const countryStore = useCountryStore();

const showInvalidFeedback = ref(false);

const {
    aboriginal_and_torres_strait_islander,
    gender,
    highest_qualification_place_code,
    highest_qualification_code,
    work_contract_code,
    staff_work_level_code,
    organisational_unit_code,
    work_sector_code,
    function_code,
} = tcsiConfig;

const genderOptions = computed(() => {
    return Object.entries(gender).map(([key, value]) => ({
        text: value,
        value: key,
    }));
});

const atsiCodesOptions = computed(() => {
    return Object.entries(aboriginal_and_torres_strait_islander).map(([key, value]) => ({
        text: value,
        value: key,
    }));
});

const highestQualificationOptions = computed(() => {
    return Object.entries(highest_qualification_code).map(([key, value]) => ({
        text: value,
        value: key,
    }));
});

const highestQualificationPlaceOptions = computed(() => {
    return Object.entries(highest_qualification_place_code).map(([key, value]) => ({
        text: value,
        value: key,
    }));
});

const workContractOptions = computed(() => {
    return Object.entries(work_contract_code).map(([key, value]) => ({
        text: value,
        value: key,
    }));
});

const workSectorOptions = computed(() => {
    return Object.entries(work_sector_code).map(([key, value]) => ({
        text: value,
        value: key,
    }));
});

const functionOptions = computed(() => {
    return Object.entries(function_code).map(([key, value]) => ({
        text: value,
        value: key,
    }));
});

const workLevelOptions = computed(() => {
    return Object.entries(staff_work_level_code).map(([key, value]) => ({
        text: value,
        value: key,
    }));
});

const unitCodeOptions = computed(() => {
    return Object.entries(organisational_unit_code).map(([key, value]) => ({
        text: value,
        value: key,
    }));
});

const handleStaffRoleChange = (e) => {
    const value = e.value;
    kendoForm.onChange('staff_role', {
        value: value,
    });
    staffStore.filters.position = value;
};

watch(
    () => props.store.isFormValid,
    (val) => {
        console.log('valll', val);
        if (!val) {
            showInvalidFeedback.value = true;

            setTimeout(() => {
                showInvalidFeedback.value = false;
                props.store.isFormValid = true;
            }, 3000);
        }
    }
);
</script>
<style lang=""></style>
