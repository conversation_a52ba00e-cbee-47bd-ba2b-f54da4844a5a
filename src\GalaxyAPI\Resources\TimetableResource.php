<?php

namespace GalaxyAPI\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class TimetableResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'college_id' => $this->college_id,
            'campus_id' => $this->campus_id,
            // "campus" => new CampusResource($this->whenLoaded('campus')),
            'course_type_id' => $this->course_type_id,
            'calendar_id' => $this->calendar_id,
            'semester_id' => $this->semester_id,
            'semester' => $this->whenLoaded('semester', function () {
                return [
                    'id' => $this->semester->id,
                    'semester_name' => $this->semester->id,
                    'year' => $this->semester->id,
                    'calendar_type' => $this->semester->id,
                    'semester_start' => parseDate($this->semester_start),
                    'semester_finish' => parseDate($this->semester_finish),
                    'last_term_date' => parseDate($this->last_term_date),
                ];
            }),
            'term' => $this->term,
            'year' => $this->year,
            'start_week' => parseDate($this->start_week),
            'end_week' => parseDate($this->end_week),
            'start_week_id' => $this->start_week_id,
            'end_week_id' => $this->end_week_id,
            'assessor' => $this->assessor,
            'census_date' => parseDate($this->census_date),
            'subject_id' => $this->subject_id,
            'batch' => $this->batch,
            'teacher_id' => $this->teacher_id,
            'attendance_type' => $this->attendance_type,
            'day' => $this->day,
            'class_type' => $this->class_type,
            'default_time_table' => $this->default_time_table,
            'start_time' => parseTime($this->start_time),
            'finish_time' => parseTime($this->finish_time),
            'break_from' => parseTime($this->break_from),
            'break_to' => parseTime($this->break_to),
            'venue_id' => parseTime($this->venue_id),
            // "venue" => new VenueResource($this->whenLoaded('venue')),
            'classroom_id' => $this->classroom_id,
            // "classroom" => new VenueResource($this->whenLoaded('classroom')),
            'class_capacity' => $this->class_capacity,
            'change_type' => $this->change_type,
            'relief_from_week' => parseDate($this->relief_from_week),
            'relief_to_week' => parseDate($this->relief_to_week),
            'relief_start_date' => parseDate($this->relief_start_date),
            'relief_end_date' => parseDate($this->relief_end_date),
            'relief_new_teacher_id' => $this->relief_new_teacher_id,
            'is_assessment_group' => $this->is_assessment_group,
            'unified_timetable' => $this->unified_timetable,
            'timetable_settings' => $this->timetable_settings,
            'creator' => $this->whenLoaded('creator', function () {
                return [
                    'id' => $this->creator->id,
                    'college_id' => $this->creator->college_id,
                    'name' => $this->creator->name,
                    'username' => $this->creator->username,
                    'email' => $this->creator->email,
                    'phone' => $this->creator->phone,
                    'mobile' => $this->creator->mobile,
                    'profile_picture' => $this->creator->profile_picture,
                ];
            }),
            'updater' => $this->whenLoaded('updater', function () {
                return [
                    'id' => $this->updater->id,
                    'college_id' => $this->updater->college_id,
                    'name' => $this->updater->name,
                    'username' => $this->updater->username,
                    'email' => $this->updater->email,
                    'phone' => $this->updater->phone,
                    'mobile' => $this->updater->mobile,
                    'profile_picture' => $this->updater->profile_picture,
                ];
            }),
        ];
    }
}
