<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :show-refresh-button="false"
        :show-filter-button="false"
        :add-permissions="null"
        :enableSelection="false"
        :has-create-action="false"
        :create-btn-label="'Add Teacher'"
        :has-export="false"
        :has-filters="false"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
                store.selected = [];
            }
        "
        :actions="['view']"
    >
        <template #bulk-actions> </template>
        <template #body-cell-created_at="{ props }">
            <FormatDate :date="props.dataItem?.created_at" />
        </template>
        <template #body-cell-status="{ props }">
            <Badge :variant="'purple'">{{ props.dataItem?.status }}</Badge>
        </template>
        <template #body-cell-recorded_by="{ props }">
            {{ props.dataItem?.comment_by?.username }}
        </template>
        <template #body-cell-log="{ props }">
            <p class="line-clamp-4" v-html="props.dataItem?.log"></p>
        </template>
    </AsyncGrid>
    <CommunicationLogsView />
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { onMounted } from 'vue';
import FormatDate from '@spa/components/FormatDate.vue';
import Badge from '@spa/components/badges/Badge.vue';
import { useStaffCommunicationLogStore } from '@spa/stores/modules/staff/useStaffCommunicationLogStore.js';
import CommunicationLogsView from './CommunicationLogsView.vue';

const store = useStaffCommunicationLogStore();
const columns = [
    {
        name: 'created_at',
        title: 'Date/Time',
        field: 'created_at',
        sortable: true,
        replace: true,
    },
    {
        name: 'type',
        title: 'Type',
        field: 'type',
        sortable: false,
        width: 100,
    },
    {
        name: 'log',
        title: 'Log',
        field: 'log',
        sortable: true,
        replace: true,
    },
    {
        name: 'status',
        title: 'Status',
        field: 'status',
        sortable: true,
        replace: true,
        width: 100,
    },
    {
        name: 'recorded_by',
        title: 'Recorded By',
        field: 'recorded_by',
        sortable: true,
        replace: true,
    },
];
const initFilters = () => {
    store.filters = {};
};

const getBadgeVariant = (value) => {
    let badgeMapping = {
        Academic: 'purple',
        Informed: 'secondary',
    };
    return badgeMapping[value] || 'default';
};

onMounted(() => {
    console.log('onMounted', store);
    initFilters();
});
</script>
